const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;

class MarkdownEditorApp {
  constructor() {
    this.mainWindow = null;
    this.isDev = process.env.NODE_ENV === 'development';
  }

  /**
   * Create the main application window
   */
  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      icon: path.join(__dirname, 'assets', 'icon.png'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: true,
        preload: path.join(__dirname, 'preload.js')
      },
      show: false,
      titleBarStyle: 'default'
    });

    // Load the app
    this.mainWindow.loadFile('index.html');

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      
      if (this.isDev) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // Set up menu
    this.createMenu();
  }

  /**
   * Create application menu
   */
  createMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New',
            accelerator: 'CmdOrCtrl+N',
            click: () => this.sendToRenderer('menu-new-file')
          },
          {
            label: 'Open',
            accelerator: 'CmdOrCtrl+O',
            click: () => this.sendToRenderer('menu-open-file')
          },
          { type: 'separator' },
          {
            label: 'Save',
            accelerator: 'CmdOrCtrl+S',
            click: () => this.sendToRenderer('menu-save-file')
          },
          {
            label: 'Save As',
            accelerator: 'CmdOrCtrl+Shift+S',
            click: () => this.sendToRenderer('menu-save-as')
          },
          { type: 'separator' },
          {
            label: 'Export to HTML',
            click: () => this.sendToRenderer('menu-export-html')
          },
          {
            label: 'Export to PDF',
            click: () => this.sendToRenderer('menu-export-pdf')
          },
          { type: 'separator' },
          {
            label: 'Exit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => app.quit()
          }
        ]
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectall' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Help',
        submenu: [
          {
            label: 'About',
            click: () => this.showAbout()
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  /**
   * Send message to renderer process
   */
  sendToRenderer(channel, data = null) {
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  /**
   * Show about dialog
   */
  showAbout() {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'About Markdown Editor',
      message: 'Markdown Editor',
      detail: 'A modern, feature-rich Markdown editor with real-time preview.\n\nVersion 1.0.0\nBuilt with Electron'
    });
  }

  /**
   * Setup IPC handlers for file operations
   */
  setupIpcHandlers() {
    // Handle file open dialog
    ipcMain.handle('show-open-dialog', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ['openFile'],
        filters: [
          { name: 'Markdown files', extensions: ['md', 'markdown', 'txt'] },
          { name: 'All files', extensions: ['*'] }
        ]
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        try {
          const content = await fs.readFile(filePath, 'utf8');
          return {
            success: true,
            filePath,
            fileName: path.basename(filePath),
            content
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }

      return { success: false, canceled: true };
    });

    // Handle file save dialog
    ipcMain.handle('show-save-dialog', async (event, options = {}) => {
      const result = await dialog.showSaveDialog(this.mainWindow, {
        defaultPath: options.suggestedName || 'Untitled.md',
        filters: [
          { name: 'Markdown files', extensions: ['md'] },
          { name: 'Text files', extensions: ['txt'] },
          { name: 'All files', extensions: ['*'] }
        ]
      });

      if (!result.canceled && result.filePath) {
        try {
          await fs.writeFile(result.filePath, options.content || '', 'utf8');
          return {
            success: true,
            filePath: result.filePath,
            fileName: path.basename(result.filePath)
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }

      return { success: false, canceled: true };
    });

    // Handle file save (existing file)
    ipcMain.handle('save-file', async (event, filePath, content) => {
      try {
        await fs.writeFile(filePath, content, 'utf8');
        return {
          success: true,
          filePath,
          fileName: path.basename(filePath)
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    // Handle directory picker
    ipcMain.handle('show-directory-dialog', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ['openDirectory']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const dirPath = result.filePaths[0];
        try {
          // Get markdown files in directory
          const files = await fs.readdir(dirPath);
          const markdownFiles = files.filter(file =>
            file.endsWith('.md') || file.endsWith('.markdown') || file.endsWith('.txt')
          );

          return {
            success: true,
            dirPath,
            dirName: path.basename(dirPath),
            files: markdownFiles
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }

      return { success: false, canceled: true };
    });

    // Handle reading file from directory
    ipcMain.handle('read-file-from-directory', async (event, dirPath, fileName) => {
      try {
        const filePath = path.join(dirPath, fileName);
        const content = await fs.readFile(filePath, 'utf8');
        return {
          success: true,
          filePath,
          fileName,
          content
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });
  }

  /**
   * Initialize the application
   */
  init() {
    // Setup IPC handlers
    this.setupIpcHandlers();

    // Handle app ready
    app.whenReady().then(() => {
      this.createWindow();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createWindow();
        }
      });
    });

    // Handle all windows closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Security: Prevent new window creation
    app.on('web-contents-created', (event, contents) => {
      contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
      });
    });
  }
}

// Create and initialize the app
const markdownEditor = new MarkdownEditorApp();
markdownEditor.init();
