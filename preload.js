const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Menu actions
  onMenuAction: (callback) => {
    ipcRenderer.on('menu-new-file', (event) => callback(event, 'menu-new-file'));
    ipcRenderer.on('menu-open-file', (event) => callback(event, 'menu-open-file'));
    ipcRenderer.on('menu-save-file', (event) => callback(event, 'menu-save-file'));
    ipcRenderer.on('menu-save-as', (event) => callback(event, 'menu-save-as'));
    ipcRenderer.on('menu-export-html', (event) => callback(event, 'menu-export-html'));
    ipcRenderer.on('menu-export-pdf', (event) => callback(event, 'menu-export-pdf'));
  },

  // File operations
  showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  saveFile: (filePath, content) => ipcRenderer.invoke('save-file', filePath, content),
  showDirectoryDialog: () => ipcRenderer.invoke('show-directory-dialog'),
  readFileFromDirectory: (dirPath, fileName) => ipcRenderer.invoke('read-file-from-directory', dirPath, fileName),

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// Expose app information
contextBridge.exposeInMainWorld('appInfo', {
  isElectron: true,
  platform: process.platform,
  version: process.versions.electron
});
